// @ts-nocheck
/**
 * Gemini AI 服务
 * 处理文本生成、图像生成和语音合成
 */

import { GoogleGenAI } from '@google/genai';
import { CreateStoryRequest, StoryPage, StoryStyle, VoiceType } from '../types/api';

export interface GeminiStoryResponse {
  title: string;
  pages: Array<{
    pageNumber: number;
    text: string;
    imagePrompt: string;
  }>;
  fullText: string;
}

export class GeminiService {
  private ai: GoogleGenAI;

  constructor(apiKey: string) {
    this.ai = new GoogleGenAI({
      apiKey: apiKey
    });
  }

  /**
   * 生成故事文本
   */
  async generateStory(request: CreateStoryRequest, subscription?: any): Promise<GeminiStoryResponse> {
    const prompt = this.buildStoryPrompt(request);
    
    // 导入订阅服务（在函数内导入避免循环依赖）
    const { SubscriptionService } = await import('./subscription');
    
    // 获取用户订阅对应的AI模型配置
    const aiConfig = SubscriptionService.getAIModelConfig(subscription);
    
    console.log(`🤖 使用${subscription?.plan || 'free'}级别AI模型: ${aiConfig.model}`);
    console.log(`   - 温度: ${aiConfig.temperature}, topK: ${aiConfig.topK}, 最大输出: ${aiConfig.maxOutputTokens}`);

    try {
      console.log('Generating story with @google/genai package...');

      const response = await this.ai.models.generateContent({
        model: aiConfig.model,
        contents: prompt,
        config: {
          temperature: aiConfig.temperature,
          topK: aiConfig.topK,
          topP: 0.95,
          maxOutputTokens: aiConfig.maxOutputTokens,
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_MEDIUM_AND_ABOVE'
            }
          ]
        }
      });

      console.log('Story generation response received');
      console.log('Response structure:', JSON.stringify(response, null, 2));

      // @google/genai的响应结构可能是 response.candidates[0].content.parts[0].text
      let text: string;
      if (response.text) {
        text = response.text;
      } else if (response.candidates && response.candidates[0] && response.candidates[0].content && response.candidates[0].content.parts && response.candidates[0].content.parts[0]) {
        text = response.candidates[0].content.parts[0].text;
      } else {
        console.error('No text in response:', response);
        throw new Error('No text content in response');
      }
      console.log('Generated text length:', text.length);

      return this.parseStoryResponse(text);
    } catch (error) {
      console.error('Story generation failed:', error);
      throw new Error(`故事生成失败: ${error.message}`);
    }
  }

  /**
   * 生成图像
   */
  async generateImages(imagePrompts: string[], style: StoryStyle, subscription?: any): Promise<string[]> {
    console.log(`🎨 [GeminiService] 开始生成 ${imagePrompts.length} 张图片，风格: ${style}`);
    
    // 导入订阅服务
    const { SubscriptionService } = await import('./subscription');
    
    // 获取用户订阅对应的图片生成配置
    const imageConfig = SubscriptionService.getImageGenerationConfig(subscription);
    
    console.log(`🖼️ 使用${subscription?.plan || 'free'}级别图片生成: ${imageConfig.model}`);
    console.log(`   - 分辨率: ${imageConfig.resolution}, 质量: ${imageConfig.quality}`);

    const stylePrompts = {
      cartoon: "卡通风格，色彩鲜艳，线条清晰，适合儿童，温馨友好",
      watercolor: "水彩画风格，柔和色调，艺术感强，梦幻效果",
      sketch: "简笔画风格，线条简洁，黑白或淡彩，手绘感",
      fantasy: "奇幻风格，梦幻色彩，魔法元素，想象力丰富",
      realistic: "写实风格，细节丰富，自然色彩，真实感强",
      anime: "动漫风格，大眼睛，夸张表情，日式风格"
    };

    const images: string[] = [];
    let successCount = 0;
    let failureCount = 0;

    for (let i = 0; i < imagePrompts.length; i++) {
      const prompt = imagePrompts[i];
      console.log(`🖼️ [GeminiService] 生成第 ${i + 1}/${imagePrompts.length} 张图片`);
      console.log(`   提示词: ${prompt.substring(0, 100)}${prompt.length > 100 ? '...' : ''}`);

      try {
        const imageResult = await this.generateSingleImageWithRetry(prompt, style, stylePrompts[style], i + 1);
        images.push(imageResult);
        successCount++;
        console.log(`✅ [GeminiService] 第 ${i + 1} 张图片生成成功`);
      } catch (error) {
        failureCount++;
        console.error(`❌ [GeminiService] 第 ${i + 1} 张图片生成失败:`, this.formatError(error));

        // 使用本地生成的占位符图片
        const placeholderBase64 = this.generatePlaceholderImage(800, 600, `第${i + 1}页`);
        images.push(placeholderBase64);
        console.log(`🔄 [GeminiService] 使用占位符图片替代第 ${i + 1} 张图片`);
      }

      // 添加延迟避免请求过快
      if (i < imagePrompts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`🎯 [GeminiService] 图片生成完成 - 成功: ${successCount}, 失败: ${failureCount}, 总计: ${images.length}`);
    return images;
  }

  /**
   * 生成单张图片（带重试机制）
   */
  private async generateSingleImageWithRetry(
    prompt: string,
    style: StoryStyle,
    styleDescription: string,
    imageNumber: number,
    maxRetries: number = 2,
    subscription?: any
  ): Promise<string> {
    let lastError: Error | null = null;
    
    // 导入订阅服务
    const { SubscriptionService } = await import('./subscription');
    
    // 获取用户订阅对应的图片生成配置
    const imageConfig = SubscriptionService.getImageGenerationConfig(subscription);
    
    // 使用订阅级别对应的重试次数
    const retries = imageConfig.maxRetries || maxRetries;

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`   🔄 [GeminiService] 第 ${imageNumber} 张图片，尝试 ${attempt}/${retries}`);

        // 根据订阅级别决定是否使用增强提示词
        let enhancedPrompt;
        if (imageConfig.enhancedPrompt) {
          enhancedPrompt = `
            ${prompt}

            风格要求：${styleDescription}
            画面要求：适合儿童观看，温馨友好，无暴力内容，高质量插图
            技术要求：${imageConfig.quality === 'ultra' ? '超高清' : imageConfig.quality === 'premium' ? '高清' : '标准清晰度'}，
                    细节${imageConfig.quality === 'standard' ? '清晰' : '丰富'}，
                    色彩和谐，${imageConfig.quality === 'ultra' ? '专业艺术级' : '专业'}插画水准
            分辨率：${imageConfig.resolution}
          `;
        } else {
          enhancedPrompt = `
            ${prompt}
            风格：${styleDescription}
            适合儿童观看，温馨友好
          `;
        }

        // 设置超时控制
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('图片生成超时 (30秒)')), 30000);
        });

        const generatePromise = this.ai.models.generateImages({
          model: imageConfig.model,
          prompt: enhancedPrompt,
          config: {
            numberOfImages: 1,
          },
        });

        const response = await Promise.race([generatePromise, timeoutPromise]);

        if (response.generatedImages && response.generatedImages.length > 0) {
          const imageBytes = response.generatedImages[0].image.imageBytes;

          // 验证base64数据
          if (!imageBytes || imageBytes.length === 0) {
            throw new Error('生成的图片数据为空');
          }

          const imageDataUrl = `data:image/png;base64,${imageBytes}`;
          console.log(`   ✅ [GeminiService] 第 ${imageNumber} 张图片生成成功，数据大小: ${imageBytes.length} 字符`);
          return imageDataUrl;
        } else {
          throw new Error('API返回的响应中没有图片数据');
        }

      } catch (error) {
        lastError = error as Error;
        console.warn(`   ⚠️ [GeminiService] 第 ${imageNumber} 张图片第 ${attempt} 次尝试失败: ${this.formatError(error)}`);

        if (attempt < maxRetries) {
          // 等待后重试
          const retryDelay = attempt * 2000; // 递增延迟
          console.log(`   ⏳ [GeminiService] ${retryDelay/1000} 秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    throw lastError || new Error(`图片生成失败，已重试 ${maxRetries} 次`);
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: any): string {
    if (error.name === 'AbortError') {
      return '请求超时';
    } else if (error.message?.includes('rate limit')) {
      return 'API请求频率限制';
    } else if (error.message?.includes('quota')) {
      return 'API配额不足';
    } else if (error.message?.includes('network')) {
      return '网络连接错误';
    } else if (error.status) {
      return `API错误 (${error.status}): ${error.message || '未知错误'}`;
    } else {
      return error.message || '未知错误';
    }
  }

  /**
   * 生成语音
   */
  async generateAudio(text: string, voice: VoiceType, subscription?: any): Promise<string> {
    console.log(`Generating audio for text length: ${text.length} characters, voice: ${voice}`);
    
    // 导入订阅服务
    const { SubscriptionService } = await import('./subscription');
    
    // 获取用户订阅对应的音频生成配置
    const audioConfig = SubscriptionService.getAudioGenerationConfig(subscription);
    
    console.log(`🔊 使用${subscription?.plan || 'free'}级别音频生成: ${audioConfig.model}`);
    console.log(`   - 音质: ${audioConfig.quality}, 可用声音: ${audioConfig.voiceOptions.join(', ')}`);

    // 检查用户是否有权限使用所选声音
    if (!audioConfig.voiceOptions.includes(voice)) {
      console.warn(`⚠️ 用户选择的声音 ${voice} 不在其订阅允许的范围内，使用默认声音`);
      voice = audioConfig.voiceOptions[0]; // 使用第一个可用声音
    }

    const voiceConfigs = {
      gentle_female: 'Kore',
      warm_male: 'Charon',
      child_friendly: 'Kore',
      storyteller: 'Charon',
      dramatic: 'Achernar', // 高级声音选项
      whispering: 'Leda'    // 高级声音选项
    };

    try {
      console.log('Generating audio with @google/genai TTS...');

      // 根据订阅级别决定文本长度限制
      // 高级订阅可以生成更长的音频
      const maxLength = audioConfig.quality === 'premium' ? 2000 : 
                        audioConfig.quality === 'high' ? 1000 : 500;
      const truncatedText = text.length > maxLength ? text.substring(0, maxLength) + '...' : text;

      const response = await this.ai.models.generateContent({
        model: audioConfig.model,
        contents: [{ parts: [{ text: `Say cheerfully: ${truncatedText}` }] }],
        config: {
          responseModalities: ['AUDIO'],
          speechConfig: {
            voiceConfig: {
              prebuiltVoiceConfig: { voiceName: voiceConfigs[voice] || 'Kore' },
            },
          },
        },
      });

      // 检查响应结构
      console.log('TTS response structure:', JSON.stringify(response, null, 2));

      const audioData = response.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;

      if (!audioData) {
        console.warn('No audio data in response, using placeholder');
        throw new Error('No audio data in response');
      }

      // 返回base64编码的音频数据
      const audioDataUrl = `data:audio/wav;base64,${audioData}`;
      console.log('Audio generation successful, data length:', audioData.length);

      return audioDataUrl;

    } catch (error) {
      console.error('Audio generation failed:', error);

      // 回退到本地生成的占位符音频（base64格式）
      console.log('Falling back to local placeholder audio...');
      const placeholderAudioBase64 = this.generatePlaceholderAudio(text.substring(0, 50));
      return placeholderAudioBase64;
    }
  }

  /**
   * 构建故事生成提示词
   */
  private buildStoryPrompt(request: CreateStoryRequest): string {
    const traits = Array.isArray(request.characterTraits) ? request.characterTraits.join('、') : '善良、勇敢';

    return `
你是一个专业的儿童故事创作者。请根据以下信息创作一个适合${request.characterAge}岁儿童的故事：

主角信息：
- 姓名：${request.characterName}
- 年龄：${request.characterAge}岁
- 性格特征：${traits}

故事设定：
- 主题：${request.theme}
- 场景：${request.setting}

创作要求：
1. 故事长度：8-10页，每页60-100字
2. 内容积极正面，富有教育意义
3. 语言简单易懂，适合${request.characterAge}岁儿童理解
4. 包含冒险、友谊、成长、学习等正面元素
5. 结局温馨美好，传递正能量
6. 避免暴力、恐怖、不当内容

输出格式要求（严格按照JSON格式）：
{
  "title": "故事标题（简洁有趣，不超过15字）",
  "pages": [
    {
      "pageNumber": 1,
      "text": "第一页的故事文字内容",
      "imagePrompt": "详细的插图描述，包含主角外观、场景、动作、情感等，用于AI绘图"
    },
    {
      "pageNumber": 2,
      "text": "第二页的故事文字内容",
      "imagePrompt": "第二页的插图描述"
    }
    // ... 继续到第8-10页
  ],
  "fullText": "完整的故事文本，用于语音合成"
}

注意事项：
- 每页的imagePrompt必须详细描述主角${request.characterName}的一致外观特征
- 确保故事情节连贯，有明确的开始、发展、高潮、结局
- 插图描述要生动具体，便于AI生成高质量图片
- 请严格按照JSON格式输出，不要添加任何其他文字
`;
  }

  /**
   * 解析故事响应
   */
  private parseStoryResponse(text: string): GeminiStoryResponse {
    try {
      // 清理可能的markdown格式
      let cleanText = text.replace(/```json\n?|\n?```/g, '').trim();

      // 检查JSON是否被截断，尝试修复
      if (!cleanText.endsWith('}')) {
        console.log('Detected truncated JSON, attempting to fix...');
        console.log('Original text length:', text.length);
        console.log('Last 200 chars:', cleanText.slice(-200));

        // 尝试找到最后一个完整的页面
        const lastCompletePageMatch = cleanText.lastIndexOf('    },\n    {');
        if (lastCompletePageMatch > 0) {
          // 截取到最后一个完整页面
          cleanText = cleanText.substring(0, lastCompletePageMatch + 6) + '\n  ]\n}';
          console.log('Fixed JSON by truncating to last complete page');
        } else {
          // 如果找不到完整页面，尝试简单的闭合
          if (cleanText.includes('"pages": [')) {
            cleanText = cleanText + '\n  ]\n}';
            console.log('Fixed JSON by adding closing brackets');
          }
        }
      }

      const parsed = JSON.parse(cleanText);

      // 验证响应格式
      if (!parsed.title || !parsed.pages || !Array.isArray(parsed.pages)) {
        throw new Error('Invalid story format');
      }

      // 验证每页的格式，跳过不完整的页面
      const validPages = [];
      for (const page of parsed.pages) {
        if (page.pageNumber && page.text && page.imagePrompt) {
          validPages.push(page);
        } else {
          console.log('Skipping incomplete page:', page);
        }
      }

      if (validPages.length === 0) {
        throw new Error('No valid pages found');
      }

      parsed.pages = validPages;
      console.log(`Successfully parsed story with ${validPages.length} valid pages`);

      return parsed;
    } catch (error) {
      console.error('Story parsing failed:', error);
      console.error('Text that failed to parse:', text.substring(0, 500) + '...');
      throw new Error('故事格式解析失败，请重试');
    }
  }

  /**
   * 内容安全检查
   */
  async checkContentSafety(text: string): Promise<boolean> {
    try {
      console.log('Performing content safety check...');

      const response = await this.ai.models.generateContent({
        model: "gemini-2.5-flash",
        contents: `请检查以下内容是否适合儿童阅读，如果适合请回复"SAFE"，如果不适合请回复"UNSAFE"并说明原因：\n\n${text}`,
        config: {
          temperature: 0.1,
          maxOutputTokens: 100,
          safetySettings: [
            {
              category: 'HARM_CATEGORY_HARASSMENT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_HATE_SPEECH',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            },
            {
              category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
              threshold: 'BLOCK_LOW_AND_ABOVE'
            }
          ]
        }
      });

      if (!response.text) {
        console.warn('Content safety check failed, assuming safe');
        return true; // 如果检查失败，默认认为安全
      }

      const result = response.text;
      console.log('Safety check result:', result);

      return result.includes('SAFE');
    } catch (error) {
      console.error('Content safety check failed:', error);
      return true; // 如果检查失败，默认认为安全
    }
  }

  /**
   * 生成占位符图片（base64格式）
   */
  private generatePlaceholderImage(width: number, height: number, text: string): string {
    // 创建一个简单的SVG占位符图片
    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f0f0f0"/>
        <rect x="10" y="10" width="${width - 20}" height="${height - 20}" fill="none" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="24" fill="#999">
          ${text}
        </text>
        <text x="50%" y="60%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#ccc">
          图片生成失败
        </text>
      </svg>
    `;

    // 将SVG转换为base64
    const base64Svg = Buffer.from(svg).toString('base64');
    return `data:image/svg+xml;base64,${base64Svg}`;
  }

  /**
   * 生成占位符音频（base64格式）
   */
  private generatePlaceholderAudio(text: string): string {
    // 创建一个简单的静音音频文件（WAV格式）
    // 这是一个44字节的WAV文件头 + 1秒的静音数据
    const sampleRate = 44100;
    const duration = 1; // 1秒
    const numSamples = sampleRate * duration;
    const numChannels = 1;
    const bytesPerSample = 2;

    const buffer = new ArrayBuffer(44 + numSamples * bytesPerSample);
    const view = new DataView(buffer);

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + numSamples * bytesPerSample, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bytesPerSample, true);
    view.setUint16(32, numChannels * bytesPerSample, true);
    view.setUint16(34, 8 * bytesPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, numSamples * bytesPerSample, true);

    // 静音数据（全部为0）
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(44 + i * bytesPerSample, 0, true);
    }

    // 转换为base64
    const uint8Array = new Uint8Array(buffer);
    const base64Audio = Buffer.from(uint8Array).toString('base64');
    return `data:audio/wav;base64,${base64Audio}`;
  }
}